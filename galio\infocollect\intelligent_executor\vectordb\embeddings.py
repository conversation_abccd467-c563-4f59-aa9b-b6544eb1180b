"""
嵌入服务
提供文本向量化和相似度计算功能
仅支持本地预置的 sentence-transformers 模型
"""

import os
import numpy as np
from typing import List, Dict, Any, Optional
import torch

from log.logger import log_info, log_error, log_debug

# 导入 sentence-transformers
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    raise ImportError("sentence-transformers is required. Please install it: pip install sentence-transformers")


class EmbeddingService:
    """
    嵌入服务
    
    仅使用本地预置的 sentence-transformers 模型
    """
    
    def __init__(self, 
                 model_name: str = "all-MiniLM-L6-v2",
                 device: Optional[str] = None):
        """
        初始化嵌入服务
        
        Args:
            model_name: 嵌入模型名称
            device: 计算设备 (cpu/cuda)
        """
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise RuntimeError("sentence-transformers not available")
            
        self.model_name = model_name
        self.model = None
        
        # 自动选择设备
        if device is None:
            device = "cuda" if torch.cuda.is_available() else "cpu"
        self.device = device
        
        # 加载本地模型
        self._load_local_model()
        log_info(f"EmbeddingService initialized with local model: {self.model_name} on {device}")

    def _load_local_model(self):
        """加载本地预置模型"""
        local_model_path = self._get_local_model_path()
        
        if not local_model_path:
            raise RuntimeError(
                f"Local model not found for {self.model_name}. "
                f"Please download the model and place it in: "
                f"{self._get_expected_model_paths()}"
            )
        
        try:
            log_info(f"Loading local model from: {local_model_path}")
            self.model = SentenceTransformer(local_model_path, device=self.device)
        except Exception as e:
            raise RuntimeError(f"Failed to load local model from {local_model_path}: {e}")

    def _get_local_model_path(self) -> Optional[str]:
        """获取本地模型路径"""
        try:
            # 获取当前文件所在目录的上级目录（intelligent_executor）
            intelligent_executor_dir = os.path.dirname(os.path.dirname(__file__))
            
            # 本地模型路径
            possible_paths = [
                # 在 models 目录下
                os.path.join(intelligent_executor_dir, 'models', self.model_name.replace('/', '_')),
                os.path.join(intelligent_executor_dir, 'models', 'sentence-transformers'),
                # 在 vectordb 目录下
                os.path.join(intelligent_executor_dir, 'vectordb', 'models', self.model_name.replace('/', '_')),
                os.path.join(intelligent_executor_dir, 'vectordb', 'models', 'sentence-transformers'),
            ]
            
            for model_path in possible_paths:
                if self._is_valid_model_path(model_path):
                    log_info(f"Found local model at: {model_path}")
                    return model_path
            
            log_error(f"Local model not found in any of these paths: {possible_paths}")
            return None
            
        except Exception as e:
            log_error(f"Error getting local model path: {e}")
            return None

    def _get_expected_model_paths(self) -> List[str]:
        """获取期望的模型路径列表（用于错误提示）"""
        intelligent_executor_dir = os.path.dirname(os.path.dirname(__file__))
        return [
            os.path.join(intelligent_executor_dir, 'models', self.model_name.replace('/', '_')),
            os.path.join(intelligent_executor_dir, 'vectordb', 'models', self.model_name.replace('/', '_')),
        ]

    def _is_valid_model_path(self, model_path: str) -> bool:
        """检查是否是有效的sentence-transformers模型路径"""
        if not os.path.exists(model_path):
            return False
        
        # 检查基础文件
        required_files = ['config.json']
        model_files = ['pytorch_model.bin', 'model.safetensors']
        
        # 必须有基础的 config.json
        if not os.path.exists(os.path.join(model_path, 'config.json')):
            return False
        
        # 必须有至少一个模型文件
        has_model_file = any(os.path.exists(os.path.join(model_path, f)) for f in model_files)
        if not has_model_file:
            return False
        
        # sentence-transformers可以加载基础的transformer模型
        # 所以只要有config.json和模型文件就认为有效
        log_debug(f"Valid model path found: {model_path}")
        return True

    def encode_text(self, text: str) -> np.ndarray:
        """
        将单个文本编码为向量
        
        Args:
            text: 输入文本
            
        Returns:
            文本向量
        """
        try:
            if not text or not text.strip():
                # 返回零向量
                return np.zeros(self.get_embedding_dimension())
            
            embedding = self.model.encode(text, convert_to_numpy=True)
            return embedding
                
        except Exception as e:
            log_error(f"Error encoding text: {e}")
            return np.zeros(self.get_embedding_dimension())

    def encode_texts(self, texts: List[str], batch_size: int = 32) -> List[np.ndarray]:
        """
        批量编码文本列表
        
        Args:
            texts: 文本列表
            batch_size: 批处理大小
            
        Returns:
            向量列表
        """
        try:
            if not texts:
                return []
            
            # 过滤空文本
            processed_texts = [text if text and text.strip() else "" for text in texts]
            
            # 批量编码
            embeddings = self.model.encode(
                processed_texts, 
                batch_size=batch_size,
                convert_to_numpy=True,
                show_progress_bar=len(texts) > 100
            )
            
            return embeddings.tolist() if isinstance(embeddings, np.ndarray) else embeddings
            
        except Exception as e:
            log_error(f"Error encoding texts: {e}")
            # 返回零向量列表
            dim = self.get_embedding_dimension()
            return [np.zeros(dim) for _ in texts]

    def get_embedding_dimension(self) -> int:
        """获取向量维度"""
        try:
            return self.model.get_sentence_embedding_dimension()
        except:
            return 384  # 默认维度

    def calculate_similarity(self, 
                           embedding1: np.ndarray, 
                           embedding2: np.ndarray,
                           method: str = "cosine") -> float:
        """
        计算两个向量的相似度
        
        Args:
            embedding1: 第一个向量
            embedding2: 第二个向量
            method: 相似度计算方法 (cosine/euclidean/dot)
            
        Returns:
            相似度分数
        """
        try:
            if method == "cosine":
                # 余弦相似度
                dot_product = np.dot(embedding1, embedding2)
                norm1 = np.linalg.norm(embedding1)
                norm2 = np.linalg.norm(embedding2)
                
                if norm1 == 0 or norm2 == 0:
                    return 0.0
                
                return dot_product / (norm1 * norm2)
                
            elif method == "euclidean":
                # 欧几里得距离（转换为相似度）
                distance = np.linalg.norm(embedding1 - embedding2)
                return 1 / (1 + distance)
                
            elif method == "dot":
                # 点积
                return np.dot(embedding1, embedding2)
                
            else:
                raise ValueError(f"Unsupported similarity method: {method}")
                
        except Exception as e:
            log_error(f"Error calculating similarity: {e}")
            return 0.0

    def find_most_similar(self, 
                         query_embedding: np.ndarray,
                         candidate_embeddings: List[np.ndarray],
                         top_k: int = 5,
                         threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        找到最相似的向量
        
        Args:
            query_embedding: 查询向量
            candidate_embeddings: 候选向量列表
            top_k: 返回前k个结果
            threshold: 相似度阈值
            
        Returns:
            相似度结果列表
        """
        try:
            similarities = []
            
            for i, candidate in enumerate(candidate_embeddings):
                similarity = self.calculate_similarity(query_embedding, candidate)
                
                if similarity >= threshold:
                    similarities.append({
                        "index": i,
                        "similarity": similarity
                    })
            
            # 按相似度排序
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            
            # 返回前k个结果
            return similarities[:top_k]
            
        except Exception as e:
            log_error(f"Error finding most similar: {e}")
            return []
    
    def encode_testcase(self, testcase: Dict[str, Any]) -> np.ndarray:
        """
        将测试用例编码为向量
        
        Args:
            testcase: 测试用例数据
            
        Returns:
            测试用例向量
        """
        try:
            # 构建测试用例文本
            text_parts = []
            
            # 添加用例名称
            if testcase.get("Testcase_Name"):
                text_parts.append(f"用例名称: {testcase['Testcase_Name']}")
            
            # 添加前提条件
            if testcase.get("Testcase_PrepareCondition"):
                text_parts.append(f"前提条件: {testcase['Testcase_PrepareCondition']}")
            
            # 添加测试步骤
            if testcase.get("Testcase_TestSteps"):
                text_parts.append(f"测试步骤: {testcase['Testcase_TestSteps']}")
            
            # 添加预期结果
            if testcase.get("Testcase_ExpectedResult"):
                text_parts.append(f"预期结果: {testcase['Testcase_ExpectedResult']}")
            
            # 添加用例标签
            if testcase.get("Testcase_Tags"):
                text_parts.append(f"标签: {testcase['Testcase_Tags']}")
            
            combined_text = " | ".join(text_parts)
            return self.encode_text(combined_text)
            
        except Exception as e:
            log_error(f"Error encoding testcase: {e}")
            return np.zeros(self.get_embedding_dimension())
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        try:
            return {
                "model_name": self.model_name,
                "device": self.device,
                "embedding_dimension": self.get_embedding_dimension(),
                "max_sequence_length": self.model.max_seq_length,
                "model_type": type(self.model).__name__
            }
        except Exception as e:
            log_error(f"Error getting model info: {e}")
            return {"error": str(e)} 