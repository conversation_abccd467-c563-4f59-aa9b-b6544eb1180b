"""
智能执行器 (Intelligent Executor)
==================================

一个基于LangChain的智能代理系统，用于自动化用例分析、执行和报告生成。

主要组件:
- agents: 各种智能代理实现
- chains: LangChain链式处理组件
- prompts: 提示词模板
- utils: 工具函数
- vectordb: 向量数据库相关功能

使用示例:
    from intelligent_executor import IntelligentExecutor
    
    executor = IntelligentExecutor()
    result = await executor.execute_case_analysis(case_info)
"""

from .core.executor import IntelligentExecutor
from .agents.case_analyzer.analyzer import CaseAnalyzer
from .agents.execution_engine.engine import ExecutionEngine
from .agents.report_generator.generator import ReportGenerator
from .vectordb.manager import VectorDBManager

__version__ = "1.0.0"
__author__ = "Intelligent Executor Team"

__all__ = [
    "IntelligentExecutor",
    "CaseAnalyzer", 
    "ExecutionEngine",
    "ReportGenerator",
    "VectorDBManager"
] 