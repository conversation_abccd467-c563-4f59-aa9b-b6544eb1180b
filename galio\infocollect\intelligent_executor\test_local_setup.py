#!/usr/bin/env python3
"""
测试本地模型和配置设置
验证用户手动下载的模型是否可以正确加载
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from infocollect.log.logger import log_info, log_error, log_debug
from intelligent_executor.utils.config_manager import ConfigManager
from intelligent_executor.vectordb.embeddings import EmbeddingService
from intelligent_executor.vectordb.manager import VectorDBManager


def test_local_model_detection():
    """测试本地模型检测"""
    print("\n=== 测试本地模型检测 ===")
    
    # 检查模型目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(current_dir, 'models', 'sentence-transformers')
    
    print(f"检查模型路径: {model_path}")
    
    if os.path.exists(model_path):
        print("✅ 模型目录存在")
        
        # 检查关键文件
        required_files = ['config.json', 'pytorch_model.bin', 'tokenizer.json']
        missing_files = []
        
        for file in required_files:
            file_path = os.path.join(model_path, file)
            if os.path.exists(file_path):
                print(f"✅ 找到文件: {file}")
            else:
                print(f"❌ 缺少文件: {file}")
                missing_files.append(file)
        
        if not missing_files:
            print("✅ 所有必需的模型文件都存在")
            return True
        else:
            print(f"❌ 缺少文件: {missing_files}")
            return False
    else:
        print("❌ 模型目录不存在")
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n=== 测试配置加载 ===")
    
    try:
        config_manager = ConfigManager()
        
        print(f"✅ 配置文件路径: {config_manager.config_file}")
        
        # 测试关键配置
        vectordb_path = config_manager.get('vectordb.db_path')
        embedding_model = config_manager.get('vectordb.embedding_model')
        
        print(f"✅ 向量数据库路径: {vectordb_path}")
        print(f"✅ 嵌入模型: {embedding_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_embedding_service():
    """测试嵌入服务"""
    print("\n=== 测试嵌入服务 ===")
    
    try:
        embedding_service = EmbeddingService()
        
        # 测试模型信息
        model_info = embedding_service.get_model_info()
        print(f"✅ 模型信息: {model_info}")
        
        # 测试文本编码
        test_text = "这是一个测试文本"
        embedding = embedding_service.encode_text(test_text)
        
        print(f"✅ 文本编码成功，向量维度: {embedding.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 嵌入服务测试失败: {e}")
        return False


def test_vector_db_setup():
    """测试向量数据库设置"""
    print("\n=== 测试向量数据库设置 ===")
    
    try:
        vectordb_manager = VectorDBManager()
        
        # 检查向量数据库状态
        status = vectordb_manager.check_vector_db_status()
        print(f"✅ 向量数据库状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量数据库设置失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试本地智能执行器设置...")
    
    tests = [
        ("本地模型检测", test_local_model_detection),
        ("配置加载", test_config_loading),
        ("嵌入服务", test_embedding_service),
        ("向量数据库设置", test_vector_db_setup),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！本地设置正确。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 