import os
import logging

from log.logger import log_debug
from flask_cors import CORS
from flask import Flask, jsonify, request

from routes.project_routes import bp as project_bp
from routes.config_routes import bp as config_bp
from routes.repository_routes import bp as repository_bp
from routes.task_routes import bp as task_bp
from routes.proxy_routes import bp as proxy_bp
from routes.file_routes import bp as file_bp
from routes.file_transfer_routes import bp as file_transfer_bp
from routes.process_routes import bp as process_bp
from routes.os_package_routes import bp as os_package_bp
from routes.hardware_routes import bp as hardware_bp
from routes.filesystem_routes import bp as filesystem_bp
from routes.port_routes import bp as port_bp
from routes.kubernetes_routes import bp as kubernetes_bp
from routes.docker_routes import bp as docker_bp
from routes.crictl_routes import bp as crictl_bp
from routes.ai_routes import bp as ai_bp
from routes.testcase_routes import bp as testcase_bp
from routes.vue_routes import bp as vue_bp
from routes.tools_routes import bp as tools_bp
from routes.node_routes import bp as node_bp
from routes.agent_log_routes import agent_log_routes
# from routes.smart.unified_vector_routes import bp as unified_vector_bp
# from routes.smart.smart_analysis_routes import bp as smart_analysis_bp
# from routes.smart.security_test_routes import bp as security_test_bp


def create_app():
    template_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../templates'))
    static_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../templates', 'static'))

    app = Flask(__name__,
                template_folder=template_path,
                static_folder=static_path)

    # 禁用 Flask 默认的日志处理器
    app.logger.handlers.clear()
    # 禁用 Werkzeug 的日志
    log = logging.getLogger('werkzeug')
    log.disabled = True

    CORS(app, resources={r"/api/*": {"origins": "*",
                                     "methods": ["GET", "POST", "DELETE", "OPTIONS"],
                                     "allow_headers": ["Content-Type", "Authorization"]}})
    register_blueprints(app)
    register_error_handlers(app)

    @app.after_request
    def log_response_info(response):
        if request.path.startswith('/api/'):
            log_debug(f"API Request: {request.method} {request.path} - Status: {response.status_code}")
        return response

    return app


def register_blueprints(app):
    blueprints = [
        (project_bp, '/api/projects'),
        (config_bp, '/api/config'),
        (repository_bp, '/api'),
        (task_bp, '/api/task'),
        (proxy_bp, '/api/proxy'),
        (file_bp, '/api/file'),
        (file_transfer_bp, '/api/file_transfer'),
        (process_bp, '/api/processes'),
        (os_package_bp, '/api/packages'),
        (hardware_bp, '/api/hardware'),
        (filesystem_bp, '/api/filesystem'),
        (port_bp, '/api/port'),
        (kubernetes_bp, '/api/k8s'),
        (docker_bp, '/api/docker'),
        (crictl_bp, '/api/crictl'),
        (ai_bp, '/api/ai'),
        (testcase_bp, '/api/testcase'),
        (tools_bp, '/api/script'),
        (node_bp, '/api/node'),
        (agent_log_routes, '/api/agent_log'),
        # (unified_vector_bp, '/api/unified_vector'),
        # (smart_analysis_bp, '/api/smart/analysis'),
        # (security_test_bp, '/api/smart/security')
    ]

    for bp, prefix in blueprints:
        app.register_blueprint(bp, url_prefix=prefix)

    app.register_blueprint(vue_bp)


def register_error_handlers(app):
    @app.errorhandler(404)
    def handle_404(e):
        return jsonify(error="Resource not found"), 404

    @app.errorhandler(500)
    def handle_500(e):
        return jsonify(error="Internal server error"), 500
