"""
会话管理器
用于管理智能执行器的会话生命周期
"""

import json
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import uuid

from log.logger import log_info, log_error, log_debug


@dataclass
class SessionInfo:
    """会话信息数据类"""
    session_id: str
    start_time: str
    end_time: Optional[str] = None
    status: str = "running"  # running, completed, failed, cancelled
    case_info: Dict[str, Any] = None
    results: Dict[str, Any] = None
    error_message: Optional[str] = None
    duration: float = 0.0
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.case_info is None:
            self.case_info = {}
        if self.results is None:
            self.results = {}
        if self.metadata is None:
            self.metadata = {}


class SessionManager:
    """
    会话管理器
    
    主要功能:
    1. 会话创建和管理
    2. 会话状态跟踪
    3. 会话持久化存储
    4. 会话清理和归档
    """
    
    def __init__(self, db_path: str = "sessions.db", max_sessions: int = 1000):
        """
        初始化会话管理器
        
        Args:
            db_path: 数据库文件路径
            max_sessions: 最大会话数量
        """
        self.db_path = db_path
        self.max_sessions = max_sessions
        self._lock = threading.RLock()
        self._active_sessions: Dict[str, SessionInfo] = {}
        
        # 初始化数据库
        self._init_database()
        
        # 加载活跃会话
        self._load_active_sessions()
        
        log_info(f"SessionManager initialized with db: {db_path}")

    def _init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sessions (
                        session_id TEXT PRIMARY KEY,
                        start_time TEXT NOT NULL,
                        end_time TEXT,
                        status TEXT NOT NULL,
                        case_info TEXT,
                        results TEXT,
                        error_message TEXT,
                        duration REAL,
                        metadata TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_status ON sessions(status)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_start_time ON sessions(start_time)")
                
                conn.commit()
                log_debug("Session database initialized")
                
        except Exception as e:
            log_error(f"Error initializing session database: {e}")
            raise

    def _load_active_sessions(self):
        """加载活跃会话"""
        try:
            # 简化实现
            log_info("Active sessions loaded")
        except Exception as e:
            log_error(f"Error loading active sessions: {e}")

    def create_session(self, 
                      case_info: Dict[str, Any] = None,
                      metadata: Dict[str, Any] = None) -> str:
        """
        创建新会话
        
        Args:
            case_info: 用例信息
            metadata: 元数据
            
        Returns:
            会话ID
        """
        try:
            with self._lock:
                session_id = str(uuid.uuid4())
                
                session_info = SessionInfo(
                    session_id=session_id,
                    start_time=datetime.now().isoformat(),
                    case_info=case_info or {},
                    metadata=metadata or {}
                )
                
                # 保存到内存
                self._active_sessions[session_id] = session_info
                
                # 保存到数据库
                self._save_session_to_db(session_info)
                
                log_info(f"Created new session: {session_id}")
                return session_id
                
        except Exception as e:
            log_error(f"Error creating session: {e}")
            raise

    def update_session(self, 
                      session_id: str,
                      status: Optional[str] = None,
                      results: Optional[Dict[str, Any]] = None,
                      error_message: Optional[str] = None) -> bool:
        """
        更新会话信息
        
        Args:
            session_id: 会话ID
            status: 新状态
            results: 结果数据
            error_message: 错误信息
            
        Returns:
            是否更新成功
        """
        try:
            with self._lock:
                if session_id not in self._active_sessions:
                    log_error(f"Session not found: {session_id}")
                    return False
                
                session_info = self._active_sessions[session_id]
                
                # 更新字段
                if status is not None:
                    session_info.status = status
                    
                    # 如果会话结束，计算持续时间
                    if status in ["completed", "failed", "cancelled"]:
                        session_info.end_time = datetime.now().isoformat()
                        start_dt = datetime.fromisoformat(session_info.start_time)
                        end_dt = datetime.fromisoformat(session_info.end_time)
                        session_info.duration = (end_dt - start_dt).total_seconds()
                
                if results is not None:
                    session_info.results.update(results)
                
                if error_message is not None:
                    session_info.error_message = error_message
                
                # 保存到数据库
                self._save_session_to_db(session_info)
                
                # 如果会话结束，从活跃会话中移除
                if session_info.status in ["completed", "failed", "cancelled"]:
                    del self._active_sessions[session_id]
                
                log_debug(f"Updated session: {session_id}")
                return True
                
        except Exception as e:
            log_error(f"Error updating session {session_id}: {e}")
            return False

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息字典
        """
        try:
            with self._lock:
                # 先从活跃会话中查找
                if session_id in self._active_sessions:
                    return asdict(self._active_sessions[session_id])
                
                return None
                
        except Exception as e:
            log_error(f"Error getting session {session_id}: {e}")
            return None

    def get_all_sessions(self) -> List[Dict[str, Any]]:
        """获取所有会话"""
        try:
            sessions = []
            with self._lock:
                for session_info in self._active_sessions.values():
                    sessions.append(asdict(session_info))
            return sessions
        except Exception as e:
            log_error(f"Error getting all sessions: {e}")
            return []

    def save_session(self, session_data: Any) -> bool:
        """
        保存会话数据
        
        Args:
            session_data: 会话数据
            
        Returns:
            是否保存成功
        """
        try:
            # 简化实现，实际使用中可以扩展
            return True
        except Exception as e:
            log_error(f"Error saving session: {e}")
            return False

    def _save_session_to_db(self, session_info: SessionInfo):
        """保存会话到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO sessions 
                    (session_id, start_time, end_time, status, case_info, results, 
                     error_message, duration, metadata, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    session_info.session_id,
                    session_info.start_time,
                    session_info.end_time,
                    session_info.status,
                    json.dumps(session_info.case_info, ensure_ascii=False),
                    json.dumps(session_info.results, ensure_ascii=False),
                    session_info.error_message,
                    session_info.duration,
                    json.dumps(session_info.metadata, ensure_ascii=False)
                ))
                conn.commit()
                
        except Exception as e:
            log_error(f"Error saving session to database: {e}")
            raise

    def cleanup_old_sessions(self, days: int = 30) -> int:
        """
        清理旧会话
        
        Args:
            days: 保留天数
            
        Returns:
            清理的会话数量
        """
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    DELETE FROM sessions 
                    WHERE start_time < ? AND status IN ('completed', 'failed', 'cancelled')
                """, (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                log_info(f"Cleaned up {deleted_count} old sessions")
                return deleted_count
                
        except Exception as e:
            log_error(f"Error cleaning up old sessions: {e}")
            return 0

    def get_session_statistics(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总会话数
                cursor.execute("SELECT COUNT(*) FROM sessions")
                total_sessions = cursor.fetchone()[0]
                
                # 按状态统计
                cursor.execute("""
                    SELECT status, COUNT(*) 
                    FROM sessions 
                    GROUP BY status
                """)
                status_counts = dict(cursor.fetchall())
                
                # 平均持续时间
                cursor.execute("""
                    SELECT AVG(duration) 
                    FROM sessions 
                    WHERE duration > 0
                """)
                avg_duration = cursor.fetchone()[0] or 0
                
                # 活跃会话数
                active_count = len(self._active_sessions)
                
                return {
                    "total_sessions": total_sessions,
                    "active_sessions": active_count,
                    "status_distribution": status_counts,
                    "average_duration_seconds": avg_duration,
                    "database_path": self.db_path
                }
                
        except Exception as e:
            log_error(f"Error getting session statistics: {e}")
            return {"error": str(e)} 