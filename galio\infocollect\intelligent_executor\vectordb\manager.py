"""
向量数据库管理器 - Milvus Lite版本
使用 Milvus Lite 作为向量数据库，sentence-transformers 作为向量化方法
高质量方案：无需Docker，轻量级部署，专业向量数据库功能，使用深度学习模型
"""

import os
import asyncio
import hashlib
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import json

try:
    from pymilvus import MilvusClient
    MILVUS_AVAILABLE = False  # 强制使用HTTP版本，解决Windows环境问题
except ImportError:
    MILVUS_AVAILABLE = False

from log.logger import log_info, log_error, log_debug, log_warning
from services.testcase_service import TestcaseService
from .embeddings import EmbeddingService


class VectorDBManager:
    """
    基于 Milvus Lite 的向量数据库管理器（使用深度学习模型）
    
    特点:
    1. 使用 sentence-transformers 进行向量化（高质量深度学习模型）
    2. 使用 Milvus Lite 作为向量存储和检索后端（无需Docker）
    3. 享受专业向量数据库的性能优势
    4. 部署简单，只需要 pip install pymilvus sentence-transformers
    5. 与完整版 Milvus 100% API 兼容，便于后续升级
    """
    
    def __init__(self, 
                 db_path: str = None,
                 collection_name: str = None,
                 embedding_model: str = None):
        """
        初始化向量数据库管理器
        
        Args:
            db_path: Milvus Lite数据库文件路径
            collection_name: Milvus集合名称
            embedding_model: 嵌入模型名称
        """
        if not MILVUS_AVAILABLE:
            raise ImportError(
                "pymilvus not installed. Install with: pip install pymilvus"
            )
        
        # 优先使用配置管理器
        try:
            from ..utils.config_manager import ConfigManager
            config = ConfigManager()
            
            # Milvus Lite配置
            self.db_path = db_path or config.get('vectordb.db_path', 'intelligent_executor/vectordb/milvus_lite.db')
            self.collection_name = collection_name or config.get('vectordb.collection_name', 'testcases_embeddings')
            self.embedding_model_name = embedding_model or config.get('vectordb.embedding_model', 'all-MiniLM-L6-v2')
        except Exception as e:
            log_warning(f"Failed to load config, using defaults: {e}")
            self.db_path = db_path or "intelligent_executor/vectordb/milvus_lite.db"
            self.collection_name = collection_name or 'testcases_embeddings'
            self.embedding_model_name = embedding_model or 'all-MiniLM-L6-v2'
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 初始化嵌入服务
        try:
            self.embedding_service = EmbeddingService(model_name=self.embedding_model_name)
            # 获取向量维度
            self.vector_dimension = self.embedding_service.model.get_sentence_embedding_dimension()
            log_info(f"Embedding service initialized with model: {self.embedding_model_name}, dimension: {self.vector_dimension}")
        except Exception as e:
            log_error(f"Failed to initialize embedding service: {e}")
            raise
        
        # Milvus Lite客户端
        self.client = None
        self.is_connected = False
        
        # 测试用例服务
        self.testcase_service = TestcaseService()
        
        # 初始化连接
        self._connect_to_milvus_lite()
        self._ensure_collection()
        
        log_info(f"VectorDBManager initialized with Milvus Lite (file: {self.db_path})")


# 如果Milvus不可用，提供HTTP版本的别名
if not MILVUS_AVAILABLE:
    try:
        from .http_manager import HttpVectorDBManager
        # 创建别名，使HttpVectorDBManager可以作为VectorDBManager使用
        VectorDBManager = HttpVectorDBManager
        log_info("Using HTTP Vector Database Manager due to Milvus unavailability")
    except ImportError as e:
        log_error(f"Failed to import HttpVectorDBManager: {e}")
        log_warning("Both Milvus and HTTP VectorDB managers are unavailable")

    def _connect_to_milvus_lite(self):
        """连接到Milvus Lite（本地文件）"""
        try:
            # 创建 Milvus Lite 客户端（本地文件模式）
            self.client = MilvusClient(self.db_path)
            self.is_connected = True
            log_info(f"Connected to Milvus Lite database: {self.db_path}")
            
        except Exception as e:
            log_error(f"Failed to connect to Milvus Lite: {e}")
            self.is_connected = False
            raise

    def _ensure_collection(self):
        """确保集合存在"""
        try:
            # 检查集合是否存在
            if self.client.has_collection(collection_name=self.collection_name):
                log_info(f"Collection '{self.collection_name}' already exists")
                return
            
            # 创建集合
            self.client.create_collection(
                collection_name=self.collection_name,
                dimension=self.vector_dimension,
                metric_type="COSINE",  # 使用余弦相似度
                auto_id=False  # 使用自定义ID
            )
            
            log_info(f"Created new collection '{self.collection_name}' with embeddings (dim: {self.vector_dimension})")
            
        except Exception as e:
            log_error(f"Error ensuring collection: {e}")
            raise

    def _build_document_text(self, testcase: Dict[str, Any]) -> str:
        """构建用于向量化的文档文本"""
        parts = []
        
        # 用例名称
        if testcase.get("Testcase_Name"):
            parts.append(f"用例名称: {testcase['Testcase_Name']}")
        
        # 前提条件
        if testcase.get("Testcase_PrepareCondition"):
            parts.append(f"前提条件: {testcase['Testcase_PrepareCondition']}")
        
        # 测试步骤
        if testcase.get("Testcase_TestSteps"):
            parts.append(f"测试步骤: {testcase['Testcase_TestSteps']}")
        
        # 预期结果
        if testcase.get("Testcase_ExpectedResult"):
            parts.append(f"预期结果: {testcase['Testcase_ExpectedResult']}")
            
        # 标签
        if testcase.get("Testcase_Tags"):
            parts.append(f"标签: {testcase['Testcase_Tags']}")
            
        # 自定义字段
        for i in range(1, 4):
            field_name = f"customField{i}"
            if testcase.get(field_name):
                parts.append(f"自定义字段{i}: {testcase[field_name]}")
        
        return " | ".join(parts)

    def _calculate_content_hash(self, content: str) -> str:
        """计算内容哈希值"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    async def sync_from_sqlite(self, force_rebuild: bool = False) -> Dict[str, Any]:
        """
        从SQLite同步数据到Milvus Lite向量数据库
        
        Args:
            force_rebuild: 是否强制重建
            
        Returns:
            同步结果
        """
        try:
            log_info("Starting sync from SQLite to Milvus Lite vector database")
            
            if not self.is_connected:
                return {"synced_count": 0, "error": "Not connected to Milvus Lite", "success": False}
            
            if force_rebuild:
                # 删除并重新创建集合
                try:
                    self.client.drop_collection(collection_name=self.collection_name)
                    log_info("Dropped existing collection for rebuild")
                except:
                    pass  # 集合可能不存在
                self._ensure_collection()
            
            # 获取所有测试用例
            testcases_result = self.testcase_service.get_all_testcases(
                page=1, 
                page_size=10000
            )
            
            testcases = testcases_result.get('data', [])
            log_info(f"Retrieved {len(testcases)} testcases from SQLite")
            
            if not testcases:
                return {"synced_count": 0, "error": "No testcases found", "success": False}
            
            # 构建文档和实体数据
            documents = []
            entities_data = []
            
            for testcase in testcases:
                doc_text = self._build_document_text(testcase)
                if not doc_text.strip():
                    continue
                
                testcase_id = f"tc_{testcase.get('id', testcase.get('Testcase_Number', ''))}"
                content_hash = self._calculate_content_hash(doc_text)
                entity_id = f"{testcase_id}_{content_hash[:8]}"
                
                # 检查是否已存在相同内容（简化版，Milvus Lite中使用get方法）
                if not force_rebuild:
                    try:
                        existing = self.client.get(
                            collection_name=self.collection_name,
                            ids=[entity_id]
                        )
                        if existing:
                            log_debug(f"Testcase {testcase_id} already exists, skipping")
                            continue
                    except:
                        pass  # ID不存在，正常情况
                
                documents.append(doc_text)
                
                # 准备实体数据
                entity = {
                    "id": entity_id,
                    "testcase_id": testcase_id,
                    "testcase_name": testcase.get('Testcase_Name', '')[:500],
                    "content_hash": content_hash,
                    "metadata": json.dumps(testcase, ensure_ascii=False)[:10000]
                }
                entities_data.append(entity)
            
            if not documents:
                return {"synced_count": 0, "error": "No new documents to sync", "success": False}
            
            # 使用 EmbeddingService 进行向量化
            log_info(f"Vectorizing {len(documents)} documents with {self.embedding_model_name}...")
            vectors = self.embedding_service.encode_texts(documents, batch_size=32)
            
            # 添加向量到实体数据
            for i, entity in enumerate(entities_data):
                entity["vector"] = vectors[i] if isinstance(vectors[i], list) else vectors[i].tolist()
            
            # 批量插入到Milvus Lite
            log_info("Inserting vectors into Milvus Lite...")
            self.client.insert(
                collection_name=self.collection_name,
                data=entities_data
            )
            
            synced_count = len(entities_data)
            log_info(f"Sync completed: {synced_count} testcases vectorized and stored in Milvus Lite")
            
            # 获取集合统计信息
            stats = self.client.get_collection_stats(collection_name=self.collection_name)
            total_count = stats.get('row_count', synced_count)
            
            return {
                "synced_count": synced_count,
                "total_in_collection": total_count,
                "success": True
            }
            
        except Exception as e:
            log_error(f"Error syncing from SQLite to Milvus Lite: {e}")
            return {"synced_count": 0, "error": str(e), "success": False}

    def search_similar_testcases(self, 
                               query: str, 
                               top_k: int = 3,
                               search_type: str = "comprehensive") -> List[Dict[str, Any]]:
        """
        在Milvus Lite中搜索相似的测试用例
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            search_type: 搜索类型 (comprehensive, name, steps)
            
        Returns:
            相似测试用例列表
        """
        try:
            if not query or not self.is_connected:
                log_debug("Query is empty or not connected")
                return []
            
            # 根据搜索类型调整查询文本
            if search_type == "name":
                query_text = f"用例名称: {query}"
            elif search_type == "steps":
                query_text = f"测试步骤: {query}"
            else:  # comprehensive
                query_text = query
            
            # 使用 EmbeddingService 向量化查询
            query_vector = self.embedding_service.encode_text(query_text)
            if query_vector.size == 0:
                return []
            
            # 在Milvus Lite中搜索
            results = self.client.search(
                collection_name=self.collection_name,
                data=[query_vector.tolist()],
                limit=top_k,
                output_fields=["testcase_id", "testcase_name", "metadata"]
            )
            
            # 处理搜索结果
            similar_testcases = []
            for result_list in results:
                for hit in result_list:
                    if hit.get('distance', 0) < 1.0:  # Milvus Lite返回距离，越小越相似
                        similarity_score = 1 - hit.get('distance', 1.0)  # 转换为相似度分数
                        
                        # 解析元数据
                        try:
                            testcase_data = json.loads(hit['entity'].get('metadata', '{}'))
                            testcase_data['similarity_score'] = float(similarity_score)
                            testcase_data['match_testcase_id'] = hit['entity'].get('testcase_id')
                            similar_testcases.append(testcase_data)
                        except (json.JSONDecodeError, KeyError) as e:
                            log_warning(f"Failed to parse result data: {e}")
                            continue
            
            log_info(f"Found {len(similar_testcases)} similar testcases for query: {query}")
            return similar_testcases
            
        except Exception as e:
            log_error(f"Error searching similar testcases in Milvus Lite: {e}")
            return []

    def get_status(self) -> Dict[str, Any]:
        """获取向量数据库状态"""
        try:
            if not self.is_connected:
                return {
                    "status": "disconnected",
                    "error": "Not connected to Milvus Lite",
                    "message": "Failed to connect to Milvus Lite database"
                }
            
            # 获取集合统计信息
            stats = self.client.get_collection_stats(collection_name=self.collection_name)
            vector_count = stats.get('row_count', 0)
            
            # 获取SQLite中的测试用例总数进行对比
            testcases_result = self.testcase_service.get_all_testcases(page=1, page_size=1)
            sqlite_total = testcases_result.get('total', 0)
            
            status = {
                "status": "connected",
                "collection_name": self.collection_name,
                "db_path": self.db_path,
                "embedding_model": self.embedding_model_name,
                "vector_count": vector_count,
                "sqlite_count": sqlite_total,
                "sync_needed": vector_count != sqlite_total,
                "initialized": True,
                "vector_dimension": self.vector_dimension,
                "database_type": "Milvus Lite"
            }
            
            if vector_count == 0:
                status["message"] = "Milvus Lite collection is empty, initialization needed"
            elif vector_count != sqlite_total:
                status["message"] = f"Milvus Lite ({vector_count}) and SQLite ({sqlite_total}) count mismatch, sync needed"
            else:
                status["message"] = "Milvus Lite vector database is up to date"
                
            return status
            
        except Exception as e:
            log_error(f"Error getting Milvus Lite status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Error getting Milvus Lite vector database status"
            }

    def check_vector_db_status(self) -> Dict[str, Any]:
        """检查向量数据库状态"""
        return self.get_status()

    async def add_testcase(self, testcase: Dict[str, Any]) -> bool:
        """
        添加单个测试用例到Milvus Lite
        
        Args:
            testcase: 测试用例数据
            
        Returns:
            是否添加成功
        """
        try:
            if not self.is_connected:
                log_warning("Not connected to Milvus Lite")
                return False
            
            doc_text = self._build_document_text(testcase)
            if not doc_text.strip():
                log_warning("Empty document text for testcase")
                return False
            
            testcase_id = f"tc_{testcase.get('id', testcase.get('Testcase_Number', ''))}"
            content_hash = self._calculate_content_hash(doc_text)
            entity_id = f"{testcase_id}_{content_hash[:8]}"
            
            # 使用 EmbeddingService 向量化文档
            vector = self.embedding_service.encode_text(doc_text)
            if vector.size == 0:
                return False
            
            # 准备实体数据
            entity_data = [{
                "id": entity_id,
                "testcase_id": testcase_id,
                "testcase_name": testcase.get('Testcase_Name', '')[:500],
                "vector": vector.tolist(),
                "content_hash": content_hash,
                "metadata": json.dumps(testcase, ensure_ascii=False)[:10000]
            }]
            
            # 插入到Milvus Lite
            self.client.insert(
                collection_name=self.collection_name,
                data=entity_data
            )
            
            log_debug(f"Added testcase to Milvus Lite: {testcase.get('Testcase_Name', 'Unknown')}")
            return True
            
        except Exception as e:
            log_error(f"Error adding testcase to Milvus Lite: {e}")
            return False

    async def update_testcase(self, testcase: Dict[str, Any]) -> bool:
        """
        更新Milvus Lite中的测试用例
        
        Args:
            testcase: 更新的测试用例数据
            
        Returns:
            是否更新成功
        """
        try:
            testcase_id = f"tc_{testcase.get('id', testcase.get('Testcase_Number', ''))}"
            
            # 删除现有记录（通过过滤器）
            self.client.delete(
                collection_name=self.collection_name,
                filter=f'testcase_id == "{testcase_id}"'
            )
            
            # 添加新记录
            return await self.add_testcase(testcase)
            
        except Exception as e:
            log_error(f"Error updating testcase in Milvus Lite: {e}")
            return False

    def get_collection_stats(self) -> Dict[str, Any]:
        """获取Milvus Lite集合统计信息"""
        try:
            if not self.is_connected:
                return {"error": "Not connected to Milvus Lite"}
            
            stats = self.client.get_collection_stats(collection_name=self.collection_name)
            
            return {
                "collection_name": self.collection_name,
                "total_documents": stats.get('row_count', 0),
                "embedding_model": self.embedding_model_name,
                "vector_dimension": self.vector_dimension,
                "db_path": self.db_path,
                "database_type": "Milvus Lite",
                "initialized": True
            }
        except Exception as e:
            log_error(f"Error getting Milvus Lite collection stats: {e}")
            return {"error": str(e)}

    async def rebuild_index(self) -> Dict[str, Any]:
        """重建Milvus Lite索引"""
        log_info("Starting Milvus Lite index rebuild")
        return await self.sync_from_sqlite(force_rebuild=True)

    def close(self):
        """关闭连接"""
        try:
            if self.client:
                self.client.close()
                log_debug("Closed Milvus Lite connection")
        except:
            pass
