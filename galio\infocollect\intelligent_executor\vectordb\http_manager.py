"""
向量数据库管理器 - HTTP客户端版本
通过HTTP API调用远程向量数据库服务
解决 Windows 环境下 milvus-lite 安装问题
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
import aiohttp
import requests

from log.logger import log_info, log_error, log_debug, log_warning
from services.testcase_service import TestcaseService


class HttpVectorDBManager:
    """
    基于HTTP API的向量数据库管理器
    
    特点:
    1. 通过HTTP API调用远程向量数据库服务
    2. 解决 Windows 环境下 milvus-lite 安装问题
    3. 与原始VectorDBManager接口兼容
    4. 支持异步和同步操作
    """
    
    def __init__(self, service_url: str = None, timeout: int = 30):
        """
        初始化HTTP向量数据库管理器
        
        Args:
            service_url: 远程向量数据库服务URL
            timeout: 请求超时时间（秒）
        """
        # 优先使用配置管理器
        try:
            from ..utils.config_manager import ConfigManager
            config = ConfigManager()
            self.service_url = service_url or config.get('vectordb.service_url', 'http://**************:9034')
        except Exception as e:
            log_warning(f"Failed to load config, using defaults: {e}")
            self.service_url = service_url or "http://localhost:8000"
        
        self.timeout = timeout
        self.is_connected = False
        
        # 测试用例服务
        self.testcase_service = TestcaseService()
        
        # 检查服务连接
        self._check_service_connection()
        
        log_info(f"HttpVectorDBManager initialized with service URL: {self.service_url}")

    def _check_service_connection(self):
        """检查与远程服务的连接"""
        try:
            response = requests.get(f"{self.service_url}/", timeout=5)
            if response.status_code == 200:
                self.is_connected = True
                log_info("Successfully connected to vector database service")
            else:
                self.is_connected = False
                log_warning(f"Vector database service returned status code: {response.status_code}")
        except Exception as e:
            self.is_connected = False
            log_warning(f"Failed to connect to vector database service: {e}")

    async def _async_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """异步HTTP请求"""
        url = f"{self.service_url}{endpoint}"
        
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                if method.upper() == "GET":
                    async with session.get(url) as response:
                        result = await response.json()
                elif method.upper() == "POST":
                    async with session.post(url, json=data) as response:
                        result = await response.json()
                else:
                    raise ValueError(f"Unsupported method: {method}")
                
                if response.status >= 400:
                    log_error(f"HTTP request failed: {response.status} - {result}")
                    return {"error": f"HTTP {response.status}: {result}"}
                
                return result
                
        except asyncio.TimeoutError:
            log_error(f"Request timeout for {endpoint}")
            return {"error": "Request timeout"}
        except Exception as e:
            log_error(f"HTTP request error for {endpoint}: {e}")
            return {"error": str(e)}

    def _sync_request(self, method: str, endpoint: str, data: Dict = None) -> Dict[str, Any]:
        """同步HTTP请求"""
        url = f"{self.service_url}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, timeout=self.timeout)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            if response.status_code >= 400:
                log_error(f"HTTP request failed: {response.status_code} - {response.text}")
                return {"error": f"HTTP {response.status_code}: {response.text}"}
            
            return response.json()
            
        except requests.exceptions.Timeout:
            log_error(f"Request timeout for {endpoint}")
            return {"error": "Request timeout"}
        except Exception as e:
            log_error(f"HTTP request error for {endpoint}: {e}")
            return {"error": str(e)}

    async def sync_from_sqlite(self, force_rebuild: bool = False) -> Dict[str, Any]:
        """
        从SQLite同步数据到远程向量数据库
        
        Args:
            force_rebuild: 是否强制重建
            
        Returns:
            同步结果
        """
        try:
            log_info("Starting sync from SQLite to remote vector database")
            
            if not self.is_connected:
                self._check_service_connection()
                if not self.is_connected:
                    return {"synced_count": 0, "error": "Cannot connect to vector database service", "success": False}
            
            # 获取所有测试用例
            testcases_result = self.testcase_service.get_all_testcases(page=1, page_size=10000)
            
            testcases = testcases_result.get('data', [])
            log_info(f"Retrieved {len(testcases)} testcases from SQLite")
            
            if not testcases:
                return {"synced_count": 0, "error": "No testcases found", "success": False}
            
            # 准备请求数据
            request_data = {
                "testcases": testcases,
                "force_rebuild": force_rebuild
            }
            
            # 发送同步请求
            result = await self._async_request("POST", "/sync", request_data)
            
            if "error" in result:
                log_error(f"Sync failed: {result['error']}")
                return {"synced_count": 0, "error": result["error"], "success": False}
            
            synced_count = result.get("synced_count", 0)
            total_in_collection = result.get("total_in_collection", synced_count)
            
            log_info(f"Sync completed: {synced_count} testcases synced to remote vector database")
            
            return {
                "synced_count": synced_count,
                "total_in_collection": total_in_collection,
                "success": True
            }
            
        except Exception as e:
            log_error(f"Error syncing from SQLite to remote vector database: {e}")
            return {"synced_count": 0, "error": str(e), "success": False}

    def search_similar_testcases(self, query: str, top_k: int = 3, search_type: str = "comprehensive") -> List[Dict[str, Any]]:
        """
        在远程向量数据库中搜索相似的测试用例
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            search_type: 搜索类型 (comprehensive, name, steps)
            
        Returns:
            相似测试用例列表
        """
        try:
            if not query:
                log_debug("Query is empty")
                return []
            
            if not self.is_connected:
                self._check_service_connection()
                if not self.is_connected:
                    log_warning("Cannot connect to vector database service")
                    return []
            
            # 准备请求数据
            request_data = {
                "query": query,
                "top_k": top_k,
                "search_type": search_type
            }
            
            # 发送搜索请求
            result = self._sync_request("POST", "/search", request_data)
            
            if "error" in result:
                log_error(f"Search failed: {result['error']}")
                return []
            
            similar_testcases = result.get("results", [])
            count = result.get("count", len(similar_testcases))
            
            log_info(f"Found {count} similar testcases for query: {query}")
            return similar_testcases
            
        except Exception as e:
            log_error(f"Error searching similar testcases in remote vector database: {e}")
            return []

    def get_status(self) -> Dict[str, Any]:
        """获取向量数据库状态"""
        try:
            if not self.is_connected:
                self._check_service_connection()
                if not self.is_connected:
                    return {
                        "status": "disconnected",
                        "error": "Cannot connect to vector database service",
                        "message": "Failed to connect to remote vector database service"
                    }
            
            # 获取远程状态
            result = self._sync_request("GET", "/status")
            
            if "error" in result:
                return {
                    "status": "error",
                    "error": result["error"],
                    "message": "Error getting remote vector database status"
                }
            
            # 获取SQLite中的测试用例总数进行对比
            testcases_result = self.testcase_service.get_all_testcases(page=1, page_size=1)
            sqlite_total = testcases_result.get('total', 0)
            
            vector_count = result.get("vector_count", 0)
            
            status = {
                "status": result.get("status", "unknown"),
                "collection_name": result.get("collection_name"),
                "service_url": self.service_url,
                "embedding_model": result.get("embedding_model"),
                "vector_count": vector_count,
                "sqlite_count": sqlite_total,
                "sync_needed": vector_count != sqlite_total,
                "initialized": result.get("initialized", False),
                "vector_dimension": result.get("vector_dimension"),
                "database_type": "Remote " + result.get("database_type", "Vector DB")
            }
            
            if vector_count == 0:
                status["message"] = "Remote vector database collection is empty, initialization needed"
            elif vector_count != sqlite_total:
                status["message"] = f"Remote vector DB ({vector_count}) and SQLite ({sqlite_total}) count mismatch, sync needed"
            else:
                status["message"] = "Remote vector database is up to date"
                
            return status
            
        except Exception as e:
            log_error(f"Error getting remote vector database status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Error getting remote vector database status"
            }

    def check_vector_db_status(self) -> Dict[str, Any]:
        """检查向量数据库状态"""
        return self.get_status()

    async def add_testcase(self, testcase: Dict[str, Any]) -> bool:
        """
        添加单个测试用例到远程向量数据库
        
        Args:
            testcase: 测试用例数据
            
        Returns:
            是否添加成功
        """
        try:
            if not self.is_connected:
                self._check_service_connection()
                if not self.is_connected:
                    log_warning("Cannot connect to vector database service")
                    return False
            
            # 发送添加请求
            result = await self._async_request("POST", "/add_testcase", testcase)
            
            if "error" in result:
                log_error(f"Add testcase failed: {result['error']}")
                return False
            
            success = result.get("success", False)
            if success:
                log_debug(f"Added testcase to remote vector database: {testcase.get('Testcase_Name', 'Unknown')}")
            
            return success
            
        except Exception as e:
            log_error(f"Error adding testcase to remote vector database: {e}")
            return False

    async def update_testcase(self, testcase: Dict[str, Any]) -> bool:
        """
        更新远程向量数据库中的测试用例
        
        Args:
            testcase: 更新的测试用例数据
            
        Returns:
            是否更新成功
        """
        # 由于远程服务会处理去重，直接添加即可
        return await self.add_testcase(testcase)

    def get_collection_stats(self) -> Dict[str, Any]:
        """获取远程向量数据库集合统计信息"""
        try:
            if not self.is_connected:
                self._check_service_connection()
                if not self.is_connected:
                    return {"error": "Cannot connect to vector database service"}
            
            result = self._sync_request("GET", "/collection_stats")
            
            if "error" in result:
                return {"error": result["error"]}
            
            # 添加服务URL信息
            result["service_url"] = self.service_url
            result["connection_type"] = "HTTP API"
            
            return result
            
        except Exception as e:
            log_error(f"Error getting remote vector database collection stats: {e}")
            return {"error": str(e)}

    async def rebuild_index(self) -> Dict[str, Any]:
        """重建远程向量数据库索引"""
        log_info("Starting remote vector database index rebuild")
        return await self.sync_from_sqlite(force_rebuild=True)

    def close(self):
        """关闭连接（HTTP客户端无需特殊关闭操作）"""
        log_debug("HttpVectorDBManager closed")
        pass 