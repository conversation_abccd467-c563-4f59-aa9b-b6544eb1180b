"""
配置管理器
用于管理智能执行器的配置信息
"""

import os
import json
from typing import Dict, Any, Optional
from infocollect.log.logger import log_info, log_error


class ConfigManager:
    """
    配置管理器
    
    主要功能:
    1. 配置文件加载
    2. 环境变量管理
    3. 默认配置提供
    """
    
    def __init__(self, config_file: str = "intelligent_executor_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = self._find_config_file(config_file)
        self.config = self._load_config()
        log_info(f"ConfigManager initialized with file: {self.config_file}")

    def _find_config_file(self, config_file: str) -> str:
        """查找配置文件，优先查找项目根目录"""
        # 当前目录
        if os.path.exists(config_file):
            return config_file
        
        # intelligent_executor 目录
        current_dir = os.path.dirname(os.path.dirname(__file__))
        config_path = os.path.join(current_dir, config_file)
        if os.path.exists(config_path):
            return config_path
        
        # 项目根目录（infocollect）
        root_dir = os.path.dirname(os.path.dirname(current_dir))
        config_path = os.path.join(root_dir, config_file)
        if os.path.exists(config_path):
            return config_path
        
        # 返回默认路径（即使不存在）
        return config_file

    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        # 默认配置 - 使用intelligent_executor目录下的vectordb
        default_config = {
            "llm": {
                "model_name": "gpt-3.5-turbo",
                "temperature": 0.1,
                "max_tokens": 2000
            },
            "vectordb": {
                "db_path": "intelligent_executor/vectordb/milvus_lite.db",
                "collection_name": "testcases_embeddings",
                "embedding_model": "all-MiniLM-L6-v2",
                "vector_dimension": 384,
                "metric_type": "COSINE"
            },
            "session": {
                "db_path": "intelligent_executor/sessions.db",
                "max_sessions": 1000,
                "cleanup_days": 30
            },
            "analysis": {
                "similarity_threshold": 0.7,
                "max_similar_cases": 5,
                "batch_size": 10
            },
            "execution": {
                "default_timeout": 30,
                "max_concurrent": 3,
                "safe_mode": True
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/intelligent_executor.log",
                "max_file_size": "10MB",
                "backup_count": 5
            }
        }
        
        try:
            # 尝试加载配置文件
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    # 深度合并配置
                    self._deep_merge(default_config, file_config)
                    log_info(f"Loaded config from file: {self.config_file}")
            else:
                log_info(f"Config file not found: {self.config_file}, using defaults")
            
            # 从环境变量覆盖配置
            self._load_from_env(default_config)
            
            return default_config
            
        except Exception as e:
            log_error(f"Error loading config: {e}")
            return default_config

    def _deep_merge(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]):
        """深度合并字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value

    def _load_from_env(self, config: Dict[str, Any]):
        """从环境变量加载配置"""
        # OpenAI API Key
        if os.getenv("OPENAI_API_KEY"):
            config.setdefault("llm", {})["api_key"] = os.getenv("OPENAI_API_KEY")
        
        # 模型名称
        if os.getenv("LLM_MODEL_NAME"):
            config.setdefault("llm", {})["model_name"] = os.getenv("LLM_MODEL_NAME")
        
        # 向量数据库路径
        if os.getenv("VECTORDB_PATH"):
            config.setdefault("vectordb", {})["db_path"] = os.getenv("VECTORDB_PATH")

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键（支持点号分隔的嵌套键）
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            log_error(f"Error getting config key {key}: {e}")
            return default

    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        try:
            keys = key.split('.')
            config = self.config
            
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
            
        except Exception as e:
            log_error(f"Error setting config key {key}: {e}")

    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            log_info(f"Config saved to: {self.config_file}")
            return True
            
        except Exception as e:
            log_error(f"Error saving config: {e}")
            return False

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config.copy() 